#!/bin/bash

# Flink CDC UDF 测试脚本
# 用于验证CDC UDF函数是否正常工作

echo "=== Flink CDC UDF 测试脚本 ==="
echo

# 检查Java版本
echo "检查Java版本..."
java -version
echo

# 编译项目
echo "编译项目..."
mvn clean compile
if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi
echo "✅ 编译成功"
echo

# 查找flink-cdc-common JAR文件
CDC_JAR=$(find ~/.m2/repository -name "flink-cdc-common-3.2.1.jar" 2>/dev/null | head -1)
if [ -z "$CDC_JAR" ]; then
    echo "❌ 找不到flink-cdc-common-3.2.1.jar，请确保Maven依赖已下载"
    exit 1
fi

echo "找到CDC JAR: $CDC_JAR"

# 运行CDC UDF测试
echo "运行CDC UDF测试..."
java -cp "target/classes:$CDC_JAR" com.longbridge.flink.udf.cdc.CdcUdfTest

if [ $? -eq 0 ]; then
    echo
    echo "🎉 CDC UDF测试全部通过！"
    echo
    echo "📋 使用说明："
    echo "1. 将编译后的JAR文件上传到Flink集群"
    echo "2. 在CDC YAML作业中注册UDF函数："
    echo "   pipeline:"
    echo "     user-defined-function:"
    echo "       - name: CDC_ENCRYPT"
    echo "         classpath: com.longbridge.flink.udf.cdc.CdcEncryptFunction"
    echo "3. 在transform中使用函数："
    echo "   transform:"
    echo "     - source-table: db.table"
    echo "       projection: id, CDC_ENCRYPT(sensitive_field) AS encrypted_field"
    echo
    echo "📁 示例文件："
    echo "   - src/main/resources/cdc-crypto-example.yaml"
    echo "   - src/main/resources/cdc-advanced-crypto-example.yaml"
else
    echo "❌ CDC UDF测试失败"
    exit 1
fi
