# Flink CDC YAML 作业示例 - 使用加密UDF函数
# 此示例展示如何在CDC YAML作业中使用自定义的加密/解密UDF函数

# 数据源配置 - MySQL CDC
source:
  type: mysql
  name: MySQL Source
  hostname: localhost
  port: 3306
  username: root
  password: password
  tables: test_db.user_info
  server-id: 5400-5404

# 数据目标配置 - Kafka
sink:
  type: kafka
  name: Kafka Sink
  properties.bootstrap.servers: localhost:9092
  topic: encrypted_user_data
  format: json

# 数据转换配置 - 使用UDF函数进行加密
transform:
  - source-table: test_db.user_info
    projection: |
      id,
      username,
      CDC_ENCRYPT(email) AS encrypted_email,
      CDC_ENCRYPT(phone, '1234567890123456') AS encrypted_phone,
      CDC_XOR_ENCODE(address) AS obfuscated_address,
      created_time,
      __table_name__ AS source_table
    filter: id > 0
    description: "加密敏感字段并添加元数据"

# 路由配置
route:
  - source-table: test_db.user_info
    sink-table: encrypted_user_data
    description: "路由加密后的用户数据到Kafka"

# 管道配置
pipeline:
  name: MySQL to Kafka with Encryption
  schema.change.behavior: LENIENT
  
  # 注册自定义UDF函数
  user-defined-function:
    - name: CDC_ENCRYPT
      classpath: com.longbridge.flink.udf.cdc.CdcEncryptFunction
    - name: CDC_DECRYPT
      classpath: com.longbridge.flink.udf.cdc.CdcDecryptFunction
    - name: CDC_XOR_ENCODE
      classpath: com.longbridge.flink.udf.cdc.CdcXorEncodeFunction
    - name: CDC_XOR_DECODE
      classpath: com.longbridge.flink.udf.cdc.CdcXorDecodeFunction
