# 高级Flink CDC YAML 作业示例 - 复杂的数据加密和转换场景
# 展示多表同步、不同加密策略、条件过滤等高级功能

# 数据源配置 - MySQL CDC (支持多表)
source:
  type: mysql
  name: MySQL Source
  hostname: ${mysql.hostname}
  port: ${mysql.port}
  username: ${mysql.username}
  password: ${mysql.password}
  tables: ${mysql.source.tables}  # 支持正则表达式，如: test_db.user_.*,test_db.order_.*
  server-id: 5400-5404

# 数据目标配置 - Hologres
sink:
  type: hologres
  name: Hologres Sink
  endpoint: ${hologres.endpoint}
  username: ${hologres.username}
  password: ${hologres.password}
  database: ${hologres.database}

# 复杂的数据转换配置
transform:
  # 用户表 - 敏感信息加密
  - source-table: test_db.user_info
    projection: |
      id,
      username,
      CDC_ENCRYPT(email) AS encrypted_email,
      CDC_ENCRYPT(phone, 'USER_PHONE_KEY_16') AS encrypted_phone,
      CDC_ENCRYPT(id_card, 'ID_CARD_KEY_16B') AS encrypted_id_card,
      CDC_XOR_ENCODE(address) AS obfuscated_address,
      age,
      gender,
      created_time,
      updated_time,
      'USER' AS data_type,
      __table_name__ AS source_table,
      __data_event_type__ AS event_type,
      NOW() AS processed_time
    filter: |
      age >= 18 AND 
      email IS NOT NULL AND 
      phone IS NOT NULL
    primary-keys: id
    description: "用户信息表 - 加密PII数据，过滤未成年用户"

  # 订单表 - 部分字段加密
  - source-table: test_db.order_info
    projection: |
      order_id,
      user_id,
      product_name,
      CDC_ENCRYPT(payment_info) AS encrypted_payment,
      amount,
      order_status,
      CDC_XOR_ENCODE(shipping_address) AS obfuscated_shipping,
      created_time,
      'ORDER' AS data_type,
      __table_name__ AS source_table,
      CASE 
        WHEN amount > 1000 THEN 'HIGH_VALUE'
        WHEN amount > 100 THEN 'MEDIUM_VALUE'
        ELSE 'LOW_VALUE'
      END AS order_category
    filter: order_status != 'CANCELLED'
    primary-keys: order_id
    description: "订单信息表 - 加密支付信息和地址，添加订单分类"

  # 日志表 - 简单混淆处理
  - source-table: test_db.user_activity_log
    projection: |
      log_id,
      user_id,
      activity_type,
      CDC_XOR_ENCODE(ip_address) AS obfuscated_ip,
      CDC_XOR_ENCODE(user_agent) AS obfuscated_ua,
      activity_time,
      'LOG' AS data_type,
      __table_name__ AS source_table
    filter: activity_time >= CURRENT_DATE - INTERVAL '30' DAY
    description: "用户活动日志 - 混淆IP和UA，只保留30天内数据"

# 路由配置 - 不同表路由到不同目标
route:
  # 用户数据路由到用户表
  - source-table: test_db.user_info
    sink-table: ods.encrypted_user_info
    description: "用户数据路由到ODS层用户表"

  # 订单数据路由到订单表
  - source-table: test_db.order_info
    sink-table: ods.encrypted_order_info
    description: "订单数据路由到ODS层订单表"

  # 日志数据路由到日志表
  - source-table: test_db.user_activity_log
    sink-table: ods.obfuscated_activity_log
    description: "活动日志路由到ODS层日志表"

# 管道配置
pipeline:
  name: Multi-Table MySQL to Hologres with Advanced Encryption
  schema.change.behavior: TRY_EVOLVE
  
  # 注册自定义UDF函数
  user-defined-function:
    - name: CDC_ENCRYPT
      classpath: com.longbridge.flink.udf.cdc.CdcEncryptFunction
    - name: CDC_DECRYPT
      classpath: com.longbridge.flink.udf.cdc.CdcDecryptFunction
    - name: CDC_XOR_ENCODE
      classpath: com.longbridge.flink.udf.cdc.CdcXorEncodeFunction
    - name: CDC_XOR_DECODE
      classpath: com.longbridge.flink.udf.cdc.CdcXorDecodeFunction
