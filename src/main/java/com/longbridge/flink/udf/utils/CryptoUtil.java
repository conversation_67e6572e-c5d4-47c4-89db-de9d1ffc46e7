package com.longbridge.flink.udf.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.util.Base64;

public class CryptoUtil {
    private static final String key0 = "FECOI()*&<MNCXZPKL";
    private static final Charset charset = Charset.forName("UTF-8");
    private static byte[] keyBytes = key0.getBytes(charset);

    public static String key = "AD42F6697B035B7580E4FEF93BE20BAD";
    private static int offset = 16;
    private static String transformation = "AES/CBC/PKCS5Padding";
    private static String algorithm = "AES";

    public static String encode(String enc) {
        if (enc == null) return null;
        byte[] b = enc.getBytes(charset);
        for (int i = 0, size = b.length; i < size; i++) {
            for (byte keyByte : keyBytes) {
                b[i] = (byte) (b[i] ^ keyByte);
            }
        }
        // 使用 Base64 编码来处理可能包含不可打印字符的字节数组
        return Base64.getEncoder().encodeToString(b);
    }

    public static String decode(String dec) {
        if (dec == null) return null;
        try {
            // 先进行 Base64 解码
            byte[] e = Base64.getDecoder().decode(dec);
            for (int i = 0, size = e.length; i < size; i++) {
                for (byte keyByte : keyBytes) {
                    e[i] = (byte) (e[i] ^ keyByte);
                }
            }
            return new String(e, charset);
        } catch (Exception ex) {
            throw new RuntimeException("XOR decode failed", ex);
        }
    }

    /**
     * 检查字符串是否为空或仅包含空白字符
     */
    private static boolean isNullOrEmpty(String content) {
        return content == null || content.trim().equals("");
    }

    /**
     * 创建并配置AES Cipher对象
     */
    private static Cipher createCipher(String key, int mode) throws Exception {
        SecretKeySpec skey = new SecretKeySpec(key.getBytes(), algorithm);
        IvParameterSpec iv = new IvParameterSpec(key.getBytes(), 0, offset);
        Cipher cipher = Cipher.getInstance(transformation);
        cipher.init(mode, skey, iv);
        return cipher;
    }

    public static String encrypt(String content) {
        if (isNullOrEmpty(content)) {
            return "";
        }
        return encrypt(content, key);
    }

    public static String decrypt(String content) {
        if (isNullOrEmpty(content)) {
            return "";
        }
        return decrypt(content, key);
    }

    public static String encrypt(String content, String key) {
        try {
            Cipher cipher = createCipher(key, Cipher.ENCRYPT_MODE);
            byte[] byteContent = content.getBytes(charset);
            byte[] result = cipher.doFinal(byteContent);
            return Base64.getEncoder().encodeToString(result);
        } catch (Exception e) {
            throw new RuntimeException("Encryption failed", e);
        }
    }

    public static String decrypt(String content, String key) {
        try {
            Cipher cipher = createCipher(key, Cipher.DECRYPT_MODE);
            byte[] result = cipher.doFinal(Base64.getDecoder().decode(content));
            return new String(result);
        } catch (Exception e) {
            throw new RuntimeException("Decryption failed", e);
        }
    }
}