package com.longbridge.flink.udf.example;

import com.longbridge.flink.udf.utils.CryptoUtil;

/**
 * 直接测试 CryptoUtil 工具类，不依赖 Flink
 */
public class CryptoUtilTest {
    
    public static void main(String[] args) {
        System.out.println("=== CryptoUtil 工具类测试 ===\n");
        
        // 测试数据
        String testData = "hello world";
        String customKey = "AD42F6697B035B7580E4FEF93BE20BAD"; // 16字节密钥
        
        System.out.println("原始数据: " + testData);
        System.out.println();
        
        // 测试 AES 加密/解密（默认密钥）
        System.out.println("=== AES 加密/解密测试（默认密钥） ===");
        try {
            String encrypted = CryptoUtil.encrypt(testData);
            String decrypted = CryptoUtil.decrypt(encrypted);
            
            System.out.println("加密后: " + encrypted);
            System.out.println("解密后: " + decrypted);
            System.out.println("测试结果: " + (testData.equals(decrypted) ? "✅ 通过" : "❌ 失败"));
        } catch (Exception e) {
            System.out.println("❌ AES 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        System.out.println();
        
        // 测试 AES 加密/解密（自定义密钥）
        System.out.println("=== AES 加密/解密测试（自定义密钥） ===");
        try {
            String encryptedCustom = CryptoUtil.encrypt(testData, customKey);
            String decryptedCustom = CryptoUtil.decrypt(encryptedCustom, customKey);
            
            System.out.println("自定义密钥: " + customKey);
            System.out.println("加密后: " + encryptedCustom);
            System.out.println("解密后: " + decryptedCustom);
            System.out.println("测试结果: " + (testData.equals(decryptedCustom) ? "✅ 通过" : "❌ 失败"));
        } catch (Exception e) {
            System.out.println("❌ AES 自定义密钥测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        System.out.println();
        
        // 测试 XOR 编码/解码
        System.out.println("=== XOR 编码/解码测试 ===");
        try {
            String encoded = CryptoUtil.encode(testData);
            String decoded = CryptoUtil.decode(encoded);
            
            System.out.println("编码后: " + encoded);
            System.out.println("解码后: " + decoded);
            System.out.println("测试结果: " + (testData.equals(decoded) ? "✅ 通过" : "❌ 失败"));
        } catch (Exception e) {
            System.out.println("❌ XOR 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        System.out.println();
        
        // 测试空值处理
        System.out.println("=== 空值处理测试 ===");
        try {
            String nullEncrypt = CryptoUtil.encrypt(null);
            String nullDecrypt = CryptoUtil.decrypt(null);
            String nullEncode = CryptoUtil.encode(null);
            String nullDecode = CryptoUtil.decode(null);
            
            System.out.println("null 加密结果: " + nullEncrypt);
            System.out.println("null 解密结果: " + nullDecrypt);
            System.out.println("null 编码结果: " + nullEncode);
            System.out.println("null 解码结果: " + nullDecode);
            System.out.println("测试结果: ✅ 空值处理正常");
        } catch (Exception e) {
            System.out.println("❌ 空值处理测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        System.out.println();
        
        // 测试空字符串处理
        System.out.println("=== 空字符串处理测试 ===");
        try {
            String emptyEncrypt = CryptoUtil.encrypt("");
            String emptyDecrypt = CryptoUtil.decrypt("");
            
            System.out.println("空字符串加密结果: '" + emptyEncrypt + "'");
            System.out.println("空字符串解密结果: '" + emptyDecrypt + "'");
            System.out.println("测试结果: ✅ 空字符串处理正常");
        } catch (Exception e) {
            System.out.println("❌ 空字符串处理测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        System.out.println();
        
        // 测试不同长度的数据
        System.out.println("=== 不同长度数据测试 ===");
        String[] testCases = {
            "a",
            "短文本",
            "这是一个中等长度的测试文本，包含中文和英文 mixed content.",
            "这是一个很长的测试文本，用来验证加密解密功能在处理大量数据时的稳定性。" +
            "Lorem ipsum dolor sit amet, consectetur adipiscing elit. " +
            "Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. " +
            "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris."
        };
        
        for (int i = 0; i < testCases.length; i++) {
            String testCase = testCases[i];
            try {
                String encrypted = CryptoUtil.encrypt(testCase);
                String decrypted = CryptoUtil.decrypt(encrypted);
                String encoded = CryptoUtil.encode(testCase);
                String decoded = CryptoUtil.decode(encoded);
                
                boolean aesPass = testCase.equals(decrypted);
                boolean xorPass = testCase.equals(decoded);
                
                System.out.println("测试案例 " + (i + 1) + " (长度: " + testCase.length() + "):");
                System.out.println("  AES: " + (aesPass ? "✅" : "❌"));
                System.out.println("  XOR: " + (xorPass ? "✅" : "❌"));
            } catch (Exception e) {
                System.out.println("测试案例 " + (i + 1) + " 失败: " + e.getMessage());
            }
        }
        
        System.out.println("\n=== 测试完成 ===");
    }
}
