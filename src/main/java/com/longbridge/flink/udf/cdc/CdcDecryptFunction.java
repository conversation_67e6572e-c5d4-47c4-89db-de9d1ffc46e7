package com.longbridge.flink.udf.cdc;

import com.longbridge.flink.udf.utils.CryptoUtil;
import org.apache.flink.cdc.common.types.DataType;
import org.apache.flink.cdc.common.types.DataTypes;
import org.apache.flink.cdc.common.udf.UserDefinedFunction;

/**
 * CDC兼容的AES解密UDF函数
 * 实现 org.apache.flink.cdc.common.udf.UserDefinedFunction 接口
 * 用于在Flink CDC YAML作业的transform模块中进行数据解密
 */
public class CdcDecryptFunction implements UserDefinedFunction {

    /**
     * 使用默认密钥解密字符串
     * 
     * @param input 待解密的Base64编码字符串
     * @return 解密后的原始字符串
     */
    public String eval(String input) {
        return CryptoUtil.decrypt(input);
    }

    /**
     * 使用自定义密钥解密字符串
     * 
     * @param input 待解密的Base64编码字符串
     * @param key 自定义密钥
     * @return 解密后的原始字符串
     */
    public String eval(String input, String key) {
        return CryptoUtil.decrypt(input, key);
    }

    /**
     * 指定返回类型为VARCHAR
     * 
     * @return VARCHAR数据类型
     */
    @Override
    public DataType getReturnType() {
        return DataTypes.VARCHAR(Integer.MAX_VALUE);
    }

    /**
     * 初始化方法，在UDF函数开始使用前调用
     */
    @Override
    public void open() throws Exception {
        // 可以在这里进行初始化操作，比如加载配置、建立连接等
        // 当前解密功能不需要特殊初始化
    }

    /**
     * 清理方法，在UDF函数结束使用后调用
     */
    @Override
    public void close() throws Exception {
        // 可以在这里进行清理操作，比如关闭连接、释放资源等
        // 当前解密功能不需要特殊清理
    }
}
