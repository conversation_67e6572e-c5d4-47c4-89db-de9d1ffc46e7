package com.longbridge.flink.udf.cdc;

/**
 * CDC UDF函数功能测试
 * 测试实现了 org.apache.flink.cdc.common.udf.UserDefinedFunction 接口的UDF函数
 */
public class CdcUdfTest {
    
    public static void main(String[] args) {
        System.out.println("=== Flink CDC UDF 功能测试 ===\n");
        
        // 测试数据
        String testData = "Hello, Longbridge CDC!";
        String chineseData = "你好，Longbridge！";
        String customKey = "1234567890123456"; // 16字节密钥
        
        try {
            // 创建CDC UDF实例
            CdcEncryptFunction cdcEncrypt = new CdcEncryptFunction();
            CdcDecryptFunction cdcDecrypt = new CdcDecryptFunction();
            CdcXorEncodeFunction cdcXorEncode = new CdcXorEncodeFunction();
            CdcXorDecodeFunction cdcXorDecode = new CdcXorDecodeFunction();
            
            // 初始化UDF函数
            cdcEncrypt.open();
            cdcDecrypt.open();
            cdcXorEncode.open();
            cdcXorDecode.open();
            
            System.out.println("原始数据: " + testData);
            System.out.println("返回类型: " + cdcEncrypt.getReturnType());
            System.out.println();
            
            // 测试AES加密解密（默认密钥）
            System.out.println("=== AES 加密解密测试（默认密钥）===");
            String encrypted = cdcEncrypt.eval(testData);
            String decrypted = cdcDecrypt.eval(encrypted);
            
            System.out.println("加密结果: " + encrypted);
            System.out.println("解密结果: " + decrypted);
            System.out.println("往返测试: " + (testData.equals(decrypted) ? "✅ 成功" : "❌ 失败"));
            System.out.println();
            
            // 测试AES加密解密（自定义密钥）
            System.out.println("=== AES 加密解密测试（自定义密钥）===");
            String encryptedCustom = cdcEncrypt.eval(testData, customKey);
            String decryptedCustom = cdcDecrypt.eval(encryptedCustom, customKey);
            
            System.out.println("自定义密钥: " + customKey);
            System.out.println("加密结果: " + encryptedCustom);
            System.out.println("解密结果: " + decryptedCustom);
            System.out.println("往返测试: " + (testData.equals(decryptedCustom) ? "✅ 成功" : "❌ 失败"));
            System.out.println();
            
            // 测试XOR编码解码
            System.out.println("=== XOR 编码解码测试 ===");
            String encoded = cdcXorEncode.eval(testData);
            String decoded = cdcXorDecode.eval(encoded);
            
            System.out.println("编码结果: " + encoded);
            System.out.println("解码结果: " + decoded);
            System.out.println("往返测试: " + (testData.equals(decoded) ? "✅ 成功" : "❌ 失败"));
            System.out.println();
            
            // 测试中文字符
            System.out.println("=== 中文字符测试 ===");
            String chineseEncrypted = cdcEncrypt.eval(chineseData);
            String chineseDecrypted = cdcDecrypt.eval(chineseEncrypted);
            String chineseEncoded = cdcXorEncode.eval(chineseData);
            String chineseDecoded = cdcXorDecode.eval(chineseEncoded);
            
            System.out.println("中文原始: " + chineseData);
            System.out.println("AES往返: " + (chineseData.equals(chineseDecrypted) ? "✅ 成功" : "❌ 失败"));
            System.out.println("XOR往返: " + (chineseData.equals(chineseDecoded) ? "✅ 成功" : "❌ 失败"));
            System.out.println();
            
            // 测试空值处理
            System.out.println("=== 空值处理测试 ===");
            String nullEncrypt = cdcEncrypt.eval(null);
            String nullDecrypt = cdcDecrypt.eval(null);
            String nullEncode = cdcXorEncode.eval(null);
            String nullDecode = cdcXorDecode.eval(null);
            
            System.out.println("null 加密结果: " + nullEncrypt);
            System.out.println("null 解密结果: " + nullDecrypt);
            System.out.println("null 编码结果: " + nullEncode);
            System.out.println("null 解码结果: " + nullDecode);
            System.out.println("空值处理: ✅ 正常");
            System.out.println();
            
            // 清理UDF函数
            cdcEncrypt.close();
            cdcDecrypt.close();
            cdcXorEncode.close();
            cdcXorDecode.close();
            
            System.out.println("=== 测试总结 ===");
            System.out.println("✅ 所有CDC UDF函数测试通过");
            System.out.println("✅ 符合 org.apache.flink.cdc.common.udf.UserDefinedFunction 接口要求");
            System.out.println("✅ 可用于Flink CDC YAML作业的transform模块");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
