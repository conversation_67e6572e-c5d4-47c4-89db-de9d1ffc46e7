# Flink CDC UDF 实现说明

## 概述

根据阿里云Flink CDC文档的要求，我们实现了符合 `org.apache.flink.cdc.common.udf.UserDefinedFunction` 接口的UDF函数，专门用于Flink CDC YAML作业的transform模块。

## 实现的UDF函数

### 1. CdcEncryptFunction
- **功能**: AES加密
- **接口**: `org.apache.flink.cdc.common.udf.UserDefinedFunction`
- **方法**:
  - `eval(String input)` - 使用默认密钥加密
  - `eval(String input, String key)` - 使用自定义密钥加密
- **返回类型**: VARCHAR
- **文件位置**: `src/main/java/com/longbridge/flink/udf/cdc/CdcEncryptFunction.java`

### 2. CdcDecryptFunction
- **功能**: AES解密
- **接口**: `org.apache.flink.cdc.common.udf.UserDefinedFunction`
- **方法**:
  - `eval(String input)` - 使用默认密钥解密
  - `eval(String input, String key)` - 使用自定义密钥解密
- **返回类型**: VARCHAR
- **文件位置**: `src/main/java/com/longbridge/flink/udf/cdc/CdcDecryptFunction.java`

### 3. CdcXorEncodeFunction
- **功能**: XOR编码
- **接口**: `org.apache.flink.cdc.common.udf.UserDefinedFunction`
- **方法**:
  - `eval(String input)` - XOR编码
- **返回类型**: VARCHAR
- **文件位置**: `src/main/java/com/longbridge/flink/udf/cdc/CdcXorEncodeFunction.java`

### 4. CdcXorDecodeFunction
- **功能**: XOR解码
- **接口**: `org.apache.flink.cdc.common.udf.UserDefinedFunction`
- **方法**:
  - `eval(String input)` - XOR解码
- **返回类型**: VARCHAR
- **文件位置**: `src/main/java/com/longbridge/flink/udf/cdc/CdcXorDecodeFunction.java`

## 符合阿里云文档要求

根据阿里云Flink CDC文档，UDF函数需要满足以下要求：

✅ **实现UserDefinedFunction接口**: 所有函数都实现了 `org.apache.flink.cdc.common.udf.UserDefinedFunction` 接口

✅ **公共无参构造器**: 所有函数都有默认的公共无参构造器

✅ **eval方法**: 每个函数都至少包含一个名为 `eval` 的公共方法

✅ **getReturnType方法**: 重写了 `getReturnType` 方法，明确指定返回类型为 `VARCHAR`

✅ **生命周期方法**: 实现了 `open` 和 `close` 方法，支持初始化和清理操作

## 在CDC YAML作业中的使用

### 1. 注册UDF函数

```yaml
pipeline:
  name: CDC Job with Encryption
  user-defined-function:
    - name: CDC_ENCRYPT
      classpath: com.longbridge.flink.udf.cdc.CdcEncryptFunction
    - name: CDC_DECRYPT
      classpath: com.longbridge.flink.udf.cdc.CdcDecryptFunction
    - name: CDC_XOR_ENCODE
      classpath: com.longbridge.flink.udf.cdc.CdcXorEncodeFunction
    - name: CDC_XOR_DECODE
      classpath: com.longbridge.flink.udf.cdc.CdcXorDecodeFunction
```

### 2. 在transform中使用

```yaml
transform:
  - source-table: db.user_info
    projection: |
      id,
      username,
      CDC_ENCRYPT(email) AS encrypted_email,
      CDC_ENCRYPT(phone, 'custom_key_16b') AS encrypted_phone,
      CDC_XOR_ENCODE(address) AS obfuscated_address,
      created_time
    filter: id > 0
```

## 测试验证

### 测试文件
- **测试类**: `src/main/java/com/longbridge/flink/udf/cdc/CdcUdfTest.java`
- **测试脚本**: `test-cdc-udf.sh`

### 测试内容
- ✅ AES加密解密（默认密钥）
- ✅ AES加密解密（自定义密钥）
- ✅ XOR编码解码
- ✅ 中文字符处理
- ✅ 空值处理
- ✅ 生命周期方法调用

### 运行测试

```bash
# 使用测试脚本
./test-cdc-udf.sh

# 手动运行
mvn clean compile
java -cp "target/classes:$(find ~/.m2/repository -name 'flink-cdc-common-3.2.1.jar')" com.longbridge.flink.udf.cdc.CdcUdfTest
```

## 示例配置

项目提供了两个完整的CDC YAML配置示例：

1. **基础示例**: `src/main/resources/cdc-crypto-example.yaml`
   - 单表同步
   - 基本加密功能
   - MySQL到Kafka

2. **高级示例**: `src/main/resources/cdc-advanced-crypto-example.yaml`
   - 多表同步
   - 复杂转换逻辑
   - 不同加密策略
   - MySQL到Hologres

## 依赖要求

- **Java**: 11+
- **Flink**: 1.20.1
- **Flink CDC**: 3.2.1
- **Maven依赖**: `flink-cdc-common:3.2.1`

## 部署说明

1. 编译项目生成JAR文件
2. 将JAR文件上传到Flink集群
3. 在CDC YAML作业中注册UDF函数
4. 在transform模块中使用函数

## 安全注意事项

- 默认加密密钥仅用于演示，生产环境请使用环境变量或安全密钥管理系统
- XOR编码仅提供最基本的混淆，不应用于高安全要求场景
- 建议在生产环境中使用自定义密钥进行加密
